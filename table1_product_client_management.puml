@startuml Table1_Product_Client_Management
!theme plain
skinparam backgroundColor #FFFFFF
skinparam sequenceArrowThickness 2
skinparam roundcorner 20
skinparam maxmessagesize 60
skinparam sequenceParticipant underline

title Table 1 - Product and Client Management (Stories 3.1, 3.2)

actor <PERSON><PERSON>eur as V
actor "Responsable\nd'entreprise" as R
participant "Contrôleur\nAuthentification" as Auth
participant "Module Gestion\nProduits" as PM
participant "Module Gestion\nClients" as CM
participant "Base Produits\nMongoDB" as DBP
participant "Base Clients\nMongoDB" as DBC
participant "Gestion\nStock" as SM

== Authentification ==
ref over V, Auth
    Authentification
end ref

V -> Auth: Connexion avec identifiants
Auth -> Auth: Vérifier les données
Auth -> V: Données vérifiées
note right of Auth: Accès autorisé

== Gestion des Produits ==
ref over V, PM, DBP, SM
    Gestion Catalogue Produits
end ref

V -> PM: Accéder module gestion produits
PM -> DBP: Récupérer produits existants
DBP -> PM: Retourner catalogue produits
PM -> V: Afficher catalogue produits

group Ajout nouveau produit
    V -> PM: Ajouter nouveau produit\n(description, prix)
    PM -> DBP: Sauvegarder nouveau produit
    DBP -> PM: Produit sauvegardé avec succès
    PM -> V: Création produit confirmée
end

group Modification produit
    V -> PM: Modifier produit existant
    PM -> DBP: Mettre à jour informations produit
    DBP -> PM: Produit mis à jour avec succès
    PM -> V: Modification produit confirmée
end

group Gestion stock
    V -> SM: Gérer stock (ajouter/soustraire/consulter)
    SM -> DBP: Mettre à jour niveaux stock
    DBP -> SM: Stock mis à jour
    SM -> V: Gestion stock terminée
end

== Gestion des Clients ==
ref over V, CM, DBC
    Gestion Base Clients
end ref

V -> CM: Accéder module gestion clients
CM -> DBC: Récupérer clients existants
DBC -> CM: Retourner base de données clients
CM -> V: Afficher liste clients

group Création client
    V -> CM: Créer nouveau client avec informations
    CM -> DBC: Sauvegarder nouveau client
    DBC -> CM: Client sauvegardé avec succès
    CM -> V: Création client confirmée
end

group Recherche client
    V -> CM: Rechercher clients existants
    CM -> DBC: Interroger base de données clients
    DBC -> CM: Retourner résultats recherche
    CM -> V: Afficher résultats recherche
end

group Modification client
    V -> CM: Modifier informations client
    CM -> DBC: Mettre à jour données client
    DBC -> CM: Client mis à jour avec succès
    CM -> V: Modification client confirmée
end

group Suppression client
    V -> CM: Supprimer client si nécessaire
    CM -> DBC: Supprimer enregistrement client
    DBC -> CM: Client supprimé avec succès
    CM -> V: Suppression client confirmée
end

@enduml
