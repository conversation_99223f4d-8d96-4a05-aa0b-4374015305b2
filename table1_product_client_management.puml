@startuml Table1_Product_Client_Management
title Table 1 - Product and Client Management (Stories 3.1, 3.2)

actor Vendeur as V
actor "Responsable d'entreprise" as R
participant "Authentication System" as Auth
participant "Product Management Module" as PM
participant "Client Management Module" as CM
participant "MongoDB Products" as DBP
participant "MongoDB Clients" as DBC
participant "Stock Management" as SM

== Authentication ==
V -> Auth: Login with credentials
Auth -> V: Authentication successful
note right: User authenticated with vendeur role

== Product Management ==
V -> PM: Access product management module
PM -> DBP: Retrieve existing products
DBP -> PM: Return product catalog
PM -> V: Display product catalog

V -> PM: Add new product (description, price)
PM -> DBP: Save new product
DBP -> PM: Product saved successfully
PM -> V: Product creation confirmed

V -> PM: Modify existing product
PM -> DBP: Update product information
DBP -> PM: Product updated successfully
PM -> V: Product modification confirmed

V -> SM: Manage stock (add/subtract/consult)
SM -> DBP: Update stock levels
DBP -> SM: Stock updated
SM -> V: Stock management completed

== Client Management ==
V -> CM: Access client management module
CM -> DBC: Retrieve existing clients
DBC -> CM: Return client database
CM -> V: Display client list

V -> CM: Create new client with information
CM -> DBC: Save new client
DBC -> CM: Client saved successfully
CM -> V: Client creation confirmed

V -> CM: Search existing clients
CM -> DBC: Query client database
DBC -> CM: Return search results
CM -> V: Display search results

V -> CM: Modify client information
CM -> DBC: Update client data
DBC -> CM: Client updated successfully
CM -> V: Client modification confirmed

V -> CM: Delete client if necessary
CM -> DBC: Remove client record
DBC -> CM: Client deleted successfully
CM -> V: Client deletion confirmed

@enduml
