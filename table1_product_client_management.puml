@startuml Table1_Product_Client_Management
title Table 1 - Product and Client Management (Stories 3.1, 3.2)

actor Vendeur as V
actor "Responsable d'entreprise" as R
participant "Authentication System" as Auth
participant "Product Management Module" as PM
participant "Client Management Module" as CM
participant "MongoDB Products" as DBP
participant "MongoDB Clients" as DBC
participant "Stock Management" as SM

== Authentication ==
activate V
V -> Auth: Login with credentials
activate Auth
Auth -> Auth: Verify data
Auth -> V: Data verified
deactivate Auth
note right of Auth: Access authorized
deactivate V

== Product Management ==
activate V
V -> PM: Access product management module
activate PM
PM -> DBP: Retrieve existing products
activate DBP
DBP -> PM: Return product catalog
deactivate DBP
PM -> V: Display product catalog
deactivate PM

V -> PM: Add new product (description, price)
activate PM
PM -> DBP: Save new product
activate DBP
DBP -> PM: Product saved successfully
deactivate DBP
PM -> V: Product creation confirmed
deactivate PM

V -> PM: Modify existing product
activate PM
PM -> DBP: Update product information
activate DBP
DBP -> PM: Product updated successfully
deactivate DBP
PM -> V: Product modification confirmed
deactivate PM

V -> SM: Manage stock (add/subtract/consult)
activate SM
SM -> DBP: Update stock levels
activate DBP
DBP -> SM: Stock updated
deactivate DBP
SM -> V: Stock management completed
deactivate SM
deactivate V

== Client Management ==
activate V
V -> CM: Access client management module
activate CM
CM -> DBC: Retrieve existing clients
activate DBC
DBC -> CM: Return client database
deactivate DBC
CM -> V: Display client list
deactivate CM

V -> CM: Create new client with information
activate CM
CM -> DBC: Save new client
activate DBC
DBC -> CM: Client saved successfully
deactivate DBC
CM -> V: Client creation confirmed
deactivate CM

V -> CM: Search existing clients
activate CM
CM -> DBC: Query client database
activate DBC
DBC -> CM: Return search results
deactivate DBC
CM -> V: Display search results
deactivate CM

V -> CM: Modify client information
activate CM
CM -> DBC: Update client data
activate DBC
DBC -> CM: Client updated successfully
deactivate DBC
CM -> V: Client modification confirmed
deactivate CM

V -> CM: Delete client if necessary
activate CM
CM -> DBC: Remove client record
activate DBC
DBC -> CM: Client deleted successfully
deactivate DBC
CM -> V: Client deletion confirmed
deactivate CM
deactivate V

@enduml
