@startuml Table4_System_Template_Configuration
!theme plain
skinparam backgroundColor #FFFFFF
skinparam sequenceArrowThickness 2
skinparam roundcorner 20
skinparam maxmessagesize 60
skinparam sequenceParticipant underline

title Table 4 - System Configuration and Template Management (Stories 3.7, 3.8)

actor Administrateur as A
actor "Responsable\nd'entreprise" as R
participant "Panneau\nAdmin" as AP
participant "Module Configuration\nSystème" as SCM
participant "Paramètres\nSécurité" as SS
participant "Module Gestion\nTemplates" as TMM
participant "Module Personnalisation\nTemplates" as TCM
participant "Base Configuration\nMongoDB" as DBC
participant "Base Templates\nMongoDB" as DBT
participant "Stockage\nFichiers" as FS

== Configuration Système par Admin ==
ref over A, SCM, SS
    Configuration Avancée Système
end ref

A -> AP: Connexion panneau admin
AP -> A: Authentification admin réussie

A -> SCM: Accéder paramètres configuration système
SCM -> DBC: Récupérer configuration actuelle
DBC -> SCM: Retourner paramètres système
SCM -> A: Afficher interface configuration

group Configuration options générales
    A -> SCM: Configurer options générales système
    SCM -> DBC: Mettre à jour paramètres généraux
    DBC -> SCM: Paramètres mis à jour avec succès
end

group Définition paramètres sécurité
    A -> SS: Définir paramètres sécurité
    SS -> DBC: Sauvegarder configuration sécurité
    DBC -> SS: Paramètres sécurité sauvegardés
    SS -> A: Configuration sécurité confirmée
end

group Création templates de base
    A -> TMM: Créer templates de base (standard et moderne)
    TMM -> DBT: Sauvegarder structures templates de base
    DBT -> TMM: Templates de base créés
    TMM -> A: Création templates de base confirmée
end

group Sauvegarde configuration
    A -> SCM: Sauvegarder configuration système
    SCM -> DBC: Persister toutes les configurations
    DBC -> SCM: Configuration sauvegardée avec succès
    SCM -> A: Configuration système terminée
end

== Personnalisation Templates par Responsable ==
ref over R, TCM, DBT, FS
    Personnalisation Templates Entreprise
end ref

R -> AP: Connexion compte entreprise
AP -> R: Authentification entreprise réussie

R -> TCM: Accéder module gestion templates
TCM -> DBT: Récupérer templates de base disponibles
DBT -> TCM: Retourner options templates
TCM -> R: Afficher templates de base (standard/moderne)

group Sélection template de base
    R -> TCM: Sélectionner template de base (standard ou moderne)
    TCM -> DBT: Charger structure template sélectionné
    DBT -> TCM: Retourner détails template
    TCM -> R: Afficher interface personnalisation template
end

group Personnalisation couleurs
    R -> TCM: Personnaliser couleurs selon charte graphique
    TCM -> DBT: Mettre à jour schéma couleurs template
    DBT -> TCM: Couleurs mises à jour avec succès
end

group Ajout logo entreprise
    R -> TCM: Ajouter logo entreprise
    TCM -> FS: Télécharger et stocker fichier logo
    FS -> TCM: Logo stocké avec succès
    TCM -> DBT: Lier logo au template
    DBT -> TCM: Association logo terminée
end

group Application template personnalisé
    R -> TCM: Appliquer template personnalisé aux documents
    TCM -> DBT: Sauvegarder template personnalisé
    DBT -> TCM: Personnalisation template sauvegardée
    TCM -> R: Application template confirmée
end

== Prévisualisation et Validation ==
ref over R, TCM, DBT
    Prévisualisation et Finalisation
end ref

group Prévisualisation modifications
    R -> TCM: Prévisualiser et valider modifications
    TCM -> DBT: Générer aperçu template
    DBT -> TCM: Retourner données aperçu
    TCM -> R: Afficher aperçu template
end

group Validation finale
    R -> TCM: Valider et confirmer modifications
    TCM -> DBT: Finaliser personnalisation template
    DBT -> TCM: Template finalisé avec succès
    TCM -> R: Personnalisation template terminée
end

@enduml
