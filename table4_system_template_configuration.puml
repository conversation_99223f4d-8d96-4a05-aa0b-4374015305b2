@startuml Table4_System_Template_Configuration
title Table 4 - System Configuration and Template Management (Stories 3.7, 3.8)

actor Administrateur as A
actor "Responsable d'entreprise" as R
participant "Admin Panel" as AP
participant "System Configuration Module" as SCM
participant "Security Settings" as SS
participant "Template Management Module" as TMM
participant "Template Customization Module" as TCM
participant "MongoDB Config" as DBC
participant "MongoDB Templates" as DBT
participant "File Storage" as FS

== System Configuration by Admin ==
A -> AP: Login to admin panel
AP -> A: Admin authentication successful

A -> SCM: Access system configuration settings
SCM -> DBC: Retrieve current configuration
DBC -> SCM: Return system parameters
SCM -> A: Display configuration interface

A -> SCM: Configure general system options
SCM -> DBC: Update general settings
DBC -> SCM: Settings updated successfully

A -> SS: Define security parameters
SS -> DBC: Save security configuration
DBC -> SS: Security settings saved
SS -> A: Security configuration confirmed

A -> TMM: Create base templates (standard and modern)
TMM -> DBT: Save base template structures
DBT -> TMM: Base templates created
TMM -> A: Base template creation confirmed

A -> SCM: Save system configuration
SCM -> DBC: Persist all configurations
DBC -> SCM: Configuration saved successfully
SCM -> A: System configuration completed

== Template Customization by Enterprise Manager ==
R -> AP: Login to enterprise account
AP -> R: Enterprise authentication successful

R -> TCM: Access template management module
TCM -> DBT: Retrieve available base templates
DBT -> TCM: Return template options
TCM -> R: Display base templates (standard/modern)

R -> TCM: Select base template (standard or modern)
TCM -> DBT: Load selected template structure
DBT -> TCM: Return template details
TCM -> R: Display template customization interface

R -> TCM: Customize colors according to brand guidelines
TCM -> DBT: Update template color scheme
DBT -> TCM: Colors updated successfully

R -> TCM: Add company logo
TCM -> FS: Upload and store logo file
FS -> TCM: Logo stored successfully
TCM -> DBT: Link logo to template
DBT -> TCM: Logo association completed

R -> TCM: Apply personalized template to documents
TCM -> DBT: Save customized template
DBT -> TCM: Template customization saved
TCM -> R: Template application confirmed

R -> TCM: Preview and validate modifications
TCM -> DBT: Generate template preview
DBT -> TCM: Return preview data
TCM -> R: Display template preview
R -> TCM: Validate and confirm changes
TCM -> DBT: Finalize template customization
DBT -> TCM: Template finalized successfully
TCM -> R: Template customization completed

@enduml
