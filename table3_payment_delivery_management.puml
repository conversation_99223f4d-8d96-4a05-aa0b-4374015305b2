@startuml Table3_Payment_Delivery_Management
title Table 3 - Payment and Delivery Management (Stories 3.5, 3.6)

actor <PERSON><PERSON><PERSON><PERSON><PERSON> as U
actor Vendeur as V
actor "Responsable d'entreprise" as R
actor <PERSON><PERSON>ur as L
participant "Payment Management Module" as PMM
participant "Delivery Management Module" as DMM
participant "Invoice System" as IS
participant "Delivery Note System" as DNS
participant "Statistics Module" as SM
participant "MongoDB Payments" as DBP
participant "MongoDB Deliveries" as DBD
participant "MongoDB Invoices" as DBI

== Payment Management ==
activate U
U -> PMM: Access payment management module
activate PMM
PMM -> DBI: Retrieve pending invoices
activate DBI
DBI -> PMM: Return invoice list
deactivate DBI
PMM -> U: Display invoices to process
deactivate PMM

U -> PMM: Select invoice to process
activate PMM
PMM -> DBI: Retrieve invoice details
activate DBI
DBI -> PMM: Return invoice information
deactivate DBI
PMM -> U: Display invoice details
deactivate PMM

U -> PMM: Choose payment method (transfer/check/cash)
activate PMM
PMM -> U: Display payment method options
deactivate PMM

U -> PMM: Record received payment
activate PMM
PMM -> DBP: Save payment information
activate DBP
DBP -> PMM: Payment recorded successfully
deactivate DBP
PMM -> DBI: Update invoice status to paid
activate DBI
DBI -> PMM: Invoice status updated
deactivate DBI
PMM -> U: Payment recording confirmed
deactivate PMM

U -> PMM: Track invoice status (paid/unpaid)
activate PMM
PMM -> DBI: Query invoice statuses
activate DBI
DBI -> PMM: Return status information
deactivate DBI
PMM -> U: Display invoice tracking
deactivate PMM
deactivate U

== Delivery Management ==
activate V
V -> DMM: Access delivery management module
activate DMM
DMM -> DBD: Retrieve available deliverers
activate DBD
DBD -> DMM: Return deliverer list
deactivate DBD
DMM -> V: Display available deliverers
deactivate DMM

V -> DNS: Create delivery note for invoice
activate DNS
DNS -> DBI: Link delivery note to invoice
activate DBI
DBI -> DNS: Delivery note created
deactivate DBI
DNS -> V: Delivery note creation confirmed
deactivate DNS

V -> DMM: Assign deliverer to delivery
activate DMM
DMM -> DBD: Assign deliverer to delivery
activate DBD
DBD -> DMM: Assignment completed
deactivate DBD
DMM -> L: Notify deliverer of assignment
activate L
deactivate L
DMM -> V: Deliverer assignment confirmed
deactivate DMM

V -> DMM: Track delivery status in real-time
activate DMM
DMM -> DBD: Query delivery status
activate DBD
DBD -> DMM: Return current status
deactivate DBD
DMM -> V: Display delivery tracking
deactivate DMM
deactivate V

== Statistics and Performance ==
activate R
R -> SM: Consult deliverer performance statistics
activate SM
SM -> DBD: Retrieve delivery data
activate DBD
DBD -> SM: Return performance metrics
deactivate DBD
SM -> R: Display performance statistics
deactivate SM

R -> SM: Generate detailed delivery reports
activate SM
SM -> DBD: Query comprehensive delivery data
activate DBD
DBD -> SM: Return detailed information
deactivate DBD
SM -> R: Generate and display reports
deactivate SM
deactivate R

@enduml
