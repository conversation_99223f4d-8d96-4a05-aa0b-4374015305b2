@startuml Table3_Payment_Delivery_Management
title Table 3 - Payment and Delivery Management (Stories 3.5, 3.6)

actor <PERSON><PERSON><PERSON><PERSON><PERSON> as U
actor Vendeur as V
actor "Responsable d'entreprise" as R
actor Livreur as L
participant "Payment Management Module" as PMM
participant "Delivery Management Module" as DMM
participant "Invoice System" as IS
participant "Delivery Note System" as DNS
participant "Statistics Module" as SM
participant "MongoDB Payments" as DBP
participant "MongoDB Deliveries" as DBD
participant "MongoDB Invoices" as DBI

== Payment Management ==
U -> PMM: Access payment management module
PMM -> DBI: Retrieve pending invoices
DBI -> PMM: Return invoice list
PMM -> U: Display invoices to process

U -> PMM: Select invoice to process
PMM -> DBI: Retrieve invoice details
DBI -> PMM: Return invoice information
PMM -> U: Display invoice details

U -> PMM: Choose payment method (transfer/check/cash)
PMM -> U: Display payment method options

U -> PMM: Record received payment
PMM -> DBP: Save payment information
DBP -> PMM: Payment recorded successfully
PMM -> DBI: Update invoice status to paid
DBI -> PMM: Invoice status updated
PMM -> U: Payment recording confirmed

U -> PMM: Track invoice status (paid/unpaid)
PMM -> DBI: Query invoice statuses
DBI -> PMM: Return status information
PMM -> U: Display invoice tracking

== Delivery Management ==
V -> DMM: Access delivery management module
DMM -> DBD: Retrieve available deliverers
DBD -> DMM: Return deliverer list
DMM -> V: Display available deliverers

V -> DNS: Create delivery note for invoice
DNS -> DBI: Link delivery note to invoice
DBI -> DNS: Delivery note created
DNS -> V: Delivery note creation confirmed

V -> DMM: Assign deliverer to delivery
DMM -> DBD: Assign deliverer to delivery
DBD -> DMM: Assignment completed
DMM -> L: Notify deliverer of assignment
DMM -> V: Deliverer assignment confirmed

V -> DMM: Track delivery status in real-time
DMM -> DBD: Query delivery status
DBD -> DMM: Return current status
DMM -> V: Display delivery tracking

== Statistics and Performance ==
R -> SM: Consult deliverer performance statistics
SM -> DBD: Retrieve delivery data
DBD -> SM: Return performance metrics
SM -> R: Display performance statistics

R -> SM: Generate detailed delivery reports
SM -> DBD: Query comprehensive delivery data
DBD -> SM: Return detailed information
SM -> R: Generate and display reports

@enduml
