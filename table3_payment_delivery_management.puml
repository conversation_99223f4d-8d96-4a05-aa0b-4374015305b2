@startuml Table3_Payment_Delivery_Management
!theme plain
skinparam backgroundColor #FFFFFF
skinparam sequenceArrowThickness 2
skinparam roundcorner 20
skinparam maxmessagesize 60
skinparam sequenceParticipant underline

title Table 3 - Payment and Delivery Management (Stories 3.5, 3.6)

actor <PERSON><PERSON><PERSON><PERSON><PERSON> as U
actor Vendeur as V
actor "Responsable\nd'entreprise" as R
actor <PERSON><PERSON><PERSON> as L
participant "Module Gestion\nPaiements" as PMM
participant "Module Gestion\nLivraisons" as DMM
participant "Système\nFactures" as IS
participant "Système Bons\nLivraison" as DNS
participant "Module\nStatistiques" as SM
participant "Base Paiements\nMongoDB" as DBP
participant "Base Livraisons\nMongoDB" as DBD
participant "Base Factures\nMongoDB" as DBI

== Gestion des Paiements ==
ref over U, PMM, DBI
    Enregistrement et Suivi Paiements
end ref

U -> PMM: Accéder module gestion paiements
PMM -> DBI: Récupérer factures en attente
DBI -> PMM: Retourner liste factures
PMM -> U: Afficher factures à traiter

group Sélection facture
    U -> PMM: Sélectionner facture à traiter
    PMM -> DBI: Récupérer détails facture
    DBI -> PMM: Retourner informations facture
    PMM -> U: Afficher détails facture
end

group Choix méthode paiement
    U -> PMM: Choisir méthode paiement (virement/chèque/espèces)
    PMM -> U: Afficher options méthodes paiement
end

group Enregistrement paiement
    U -> PMM: Enregistrer paiement reçu
    PMM -> DBP: Sauvegarder informations paiement
    DBP -> PMM: Paiement enregistré avec succès
    PMM -> DBI: Mettre à jour statut facture payée
    DBI -> PMM: Statut facture mis à jour
    PMM -> U: Enregistrement paiement confirmé
end

group Suivi statut factures
    U -> PMM: Suivre statut factures (payées/impayées)
    PMM -> DBI: Interroger statuts factures
    DBI -> PMM: Retourner informations statut
    PMM -> U: Afficher suivi factures
end

== Gestion des Livraisons ==
ref over V, DMM, DNS, L
    Gestion Complète Livraisons
end ref

V -> DMM: Accéder module gestion livraisons
DMM -> DBD: Récupérer livreurs disponibles
DBD -> DMM: Retourner liste livreurs
DMM -> V: Afficher livreurs disponibles

group Création bon de livraison
    V -> DNS: Créer bon de livraison pour facture
    DNS -> DBI: Lier bon de livraison à facture
    DBI -> DNS: Bon de livraison créé
    DNS -> V: Création bon de livraison confirmée
end

group Attribution livreur
    V -> DMM: Attribuer livreur à livraison
    DMM -> DBD: Assigner livreur à livraison
    DBD -> DMM: Attribution terminée
    DMM -> L: Notifier livreur de l'attribution
    DMM -> V: Attribution livreur confirmée
end

group Suivi livraison temps réel
    V -> DMM: Suivre statut livraison en temps réel
    DMM -> DBD: Interroger statut livraison
    DBD -> DMM: Retourner statut actuel
    DMM -> V: Afficher suivi livraison
end

== Statistiques et Performance ==
ref over R, SM, DBD
    Analyse Performance et Rapports
end ref

group Consultation statistiques performance
    R -> SM: Consulter statistiques performance livreurs
    SM -> DBD: Récupérer données livraisons
    DBD -> SM: Retourner métriques performance
    SM -> R: Afficher statistiques performance
end

group Génération rapports détaillés
    R -> SM: Générer rapports détaillés livraisons
    SM -> DBD: Interroger données complètes livraisons
    DBD -> SM: Retourner informations détaillées
    SM -> R: Générer et afficher rapports
end

@enduml
