@startuml Table2_Subscription_Management
title Table 2 - Subscription Creation and Surveillance (Stories 3.3, 3.4)

actor <PERSON><PERSON><PERSON><PERSON><PERSON> as A
actor "Responsable d'entreprise" as R
participant "Admin System" as AS
participant "Subscription Creation Module" as SCM
participant "Subscription Surveillance Module" as SSM
participant "Alert System" as AlertS
participant "MongoDB Subscriptions" as DBS
participant "Account Management" as AM

== Admin Subscription Creation ==
activate A
A -> AS: Login to admin system
activate AS
AS -> A: Admin authentication successful
deactivate AS

A -> SCM: Access subscription creation module
activate SCM
SCM -> A: Display subscription creation interface
deactivate SCM

A -> SCM: Define variable subscription durations
activate SCM
SCM -> DBS: Save duration configurations
activate DBS
DBS -> SCM: Durations saved successfully
deactivate DBS
SCM -> A: Duration configuration confirmed
deactivate SCM

A -> SCM: Create personalized subscriptions
activate SCM
SCM -> DBS: Save subscription details
activate DBS
DBS -> SCM: Subscriptions created successfully
deactivate DBS
SCM -> A: Subscription creation confirmed
deactivate SCM

A -> SCM: Assign subscriptions to enterprise managers
activate SCM
SCM -> DBS: Link subscriptions to managers
activate DBS
DBS -> SCM: Assignments completed
deactivate DBS
SCM -> A: Subscription assignment confirmed
deactivate SCM

A -> SSM: Configure automatic subscription surveillance
activate SSM
SSM -> AlertS: Setup automatic alerts
activate AlertS
AlertS -> SSM: Alert system configured
deactivate AlertS
SSM -> A: Surveillance system activated
deactivate SSM
deactivate A

== Automatic Account Blocking ==
activate SSM
SSM -> DBS: Check subscription expiration dates
activate DBS
DBS -> SSM: Return expired subscriptions
deactivate DBS
SSM -> AM: Block expired accounts automatically
activate AM
AM -> DBS: Update account status to blocked
activate DBS
DBS -> AM: Account status updated
deactivate DBS
deactivate AM
deactivate SSM

== Enterprise Manager Consultation ==
activate R
R -> AS: Login to enterprise account
activate AS
AS -> R: Enterprise authentication successful
deactivate AS

R -> SSM: Consult subscription status
activate SSM
SSM -> DBS: Retrieve subscription information
activate DBS
DBS -> SSM: Return subscription details
deactivate DBS
SSM -> R: Display subscription status and limits
deactivate SSM

R -> SSM: Monitor usage limits
activate SSM
SSM -> DBS: Check current usage
activate DBS
DBS -> SSM: Return usage statistics
deactivate DBS
SSM -> R: Display usage monitoring
deactivate SSM
deactivate R

== Automatic Alerts and Renewal ==
activate AlertS
AlertS -> R: Send automatic alert before expiration
activate R
R -> AlertS: Acknowledge alert received
deactivate AlertS

R -> SCM: Request subscription renewal
activate SCM
SCM -> A: Forward renewal request
activate A
A -> SCM: Process renewal request
SCM -> DBS: Update subscription
activate DBS
DBS -> SCM: Renewal completed
deactivate DBS
SCM -> R: Renewal confirmation sent
deactivate SCM
deactivate A
deactivate R

@enduml
