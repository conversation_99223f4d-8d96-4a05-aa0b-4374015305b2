@startuml Table2_Subscription_Management
!theme plain
skinparam backgroundColor #FFFFFF
skinparam sequenceArrowThickness 2
skinparam roundcorner 20
skinparam maxmessagesize 60
skinparam sequenceParticipant underline

title Table 2 - Subscription Creation and Surveillance (Stories 3.3, 3.4)

actor Administrateur as A
actor "Responsable\nd'entreprise" as R
participant "Système\nAdmin" as AS
participant "Module Création\nAbonnements" as SCM
participant "Module Surveillance\nAbonnements" as SSM
participant "Système\nAlertes" as AlertS
participant "Base Abonnements\nMongoDB" as DBS
participant "Gestion\nComptes" as AM

== Création Abonnements par Admin ==
ref over A, SCM, DBS
    Création et Configuration Abonnements
end ref

A -> AS: Connexion système admin
AS -> A: Authentification admin réussie

A -> SCM: Accéder module création abonnements
SCM -> A: Afficher interface création

group Définition durées variables
    A -> SCM: Définir durées variables abonnements
    SCM -> DBS: Sauvegarder configurations durées
    DBS -> SCM: Durées sauvegardées avec succès
end

group Création abonnements personnalisés
    A -> SCM: Créer abonnements personnalisés
    SCM -> DBS: Sauvegarder détails abonnements
    DBS -> SCM: Abonnements créés avec succès
end

group Attribution aux responsables
    A -> SCM: Attribuer abonnements aux responsables
    SCM -> DBS: Lier abonnements aux gestionnaires
    DBS -> SCM: Attributions terminées
    SCM -> A: Attribution abonnements confirmée
end

== Configuration Surveillance Automatique ==
ref over A, SSM, AlertS
    Configuration Surveillance et Alertes
end ref

A -> SSM: Configurer surveillance automatique
SSM -> AlertS: Paramétrer alertes automatiques
AlertS -> SSM: Système alertes configuré
SSM -> A: Système surveillance activé

== Blocage Automatique Comptes ==
loop Vérification quotidienne
    SSM -> DBS: Vérifier dates expiration abonnements
    DBS -> SSM: Retourner abonnements expirés
    alt Abonnements expirés détectés
        SSM -> AM: Bloquer comptes expirés automatiquement
        AM -> DBS: Mettre à jour statut compte bloqué
        DBS -> AM: Statut compte mis à jour
    end
end

== Consultation par Responsable Entreprise ==
ref over R, SSM, DBS
    Consultation Statut et Surveillance
end ref

R -> AS: Connexion compte entreprise
AS -> R: Authentification entreprise réussie

group Consultation statut abonnement
    R -> SSM: Consulter statut abonnement
    SSM -> DBS: Récupérer informations abonnement
    DBS -> SSM: Retourner détails abonnement
    SSM -> R: Afficher statut et limites abonnement
end

group Surveillance limites utilisation
    R -> SSM: Surveiller limites utilisation
    SSM -> DBS: Vérifier utilisation actuelle
    DBS -> SSM: Retourner statistiques utilisation
    SSM -> R: Afficher surveillance utilisation
end

== Alertes Automatiques et Renouvellement ==
ref over AlertS, R, SCM
    Processus Alertes et Renouvellement
end ref

AlertS -> R: Envoyer alerte automatique avant expiration
R -> AlertS: Accuser réception alerte

group Demande renouvellement
    R -> SCM: Demander renouvellement abonnement
    SCM -> A: Transférer demande renouvellement
    A -> SCM: Traiter demande renouvellement
    SCM -> DBS: Mettre à jour abonnement
    DBS -> SCM: Renouvellement terminé
    SCM -> R: Confirmation renouvellement envoyée
end

@enduml
