@startuml Table2_Subscription_Management
title Table 2 - Subscription Creation and Surveillance (Stories 3.3, 3.4)

actor Ad<PERSON><PERSON><PERSON><PERSON> as A
actor "Responsable d'entreprise" as R
participant "Admin System" as AS
participant "Subscription Creation Module" as SCM
participant "Subscription Surveillance Module" as SSM
participant "Alert System" as AlertS
participant "MongoDB Subscriptions" as DBS
participant "Account Management" as AM

== Admin Subscription Creation ==
A -> AS: Login to admin system
AS -> A: Admin authentication successful

A -> SCM: Access subscription creation module
SCM -> A: Display subscription creation interface

A -> SCM: Define variable subscription durations
SCM -> DBS: Save duration configurations
DBS -> SCM: Durations saved successfully

A -> SCM: Create personalized subscriptions
SCM -> DBS: Save subscription details
DBS -> SCM: Subscriptions created successfully

A -> SCM: Assign subscriptions to enterprise managers
SCM -> DBS: Link subscriptions to managers
DBS -> SCM: Assignments completed
SCM -> A: Subscription assignment confirmed

A -> SSM: Configure automatic subscription surveillance
SSM -> AlertS: Setup automatic alerts
AlertS -> SSM: Alert system configured
SSM -> A: Surveillance system activated

== Automatic Account Blocking ==
SSM -> DBS: Check subscription expiration dates
DBS -> SSM: Return expired subscriptions
SSM -> AM: Block expired accounts automatically
AM -> DBS: Update account status to blocked
DBS -> AM: Account status updated

== Enterprise Manager Consultation ==
R -> AS: Login to enterprise account
AS -> R: Enterprise authentication successful

R -> SSM: Consult subscription status
SSM -> DBS: Retrieve subscription information
DBS -> SSM: Return subscription details
SSM -> R: Display subscription status and limits

R -> SSM: Monitor usage limits
SSM -> DBS: Check current usage
DBS -> SSM: Return usage statistics
SSM -> R: Display usage monitoring

== Automatic Alerts and Renewal ==
AlertS -> R: Send automatic alert before expiration
R -> AlertS: Acknowledge alert received

R -> SCM: Request subscription renewal
SCM -> A: Forward renewal request
A -> SCM: Process renewal request
SCM -> DBS: Update subscription
DBS -> SCM: Renewal completed
SCM -> R: Renewal confirmation sent

@enduml
