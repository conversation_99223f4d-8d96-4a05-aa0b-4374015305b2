@startuml Table1_Product_Client_Management
title Table 1 - Product and Client Management (Stories 3.1, 3.2)

actor Vendeur as V
actor "Responsable d'entreprise" as R
participant "Authentication System" as Auth
participant "Product Management Module" as PM
participant "Client Management Module" as CM
participant "MongoDB Products" as DBP
participant "MongoDB Clients" as DBC
participant "Stock Management" as SM

== Authentication ==
V -> Auth: Login with credentials
Auth -> V: Authentication successful
note right: User authenticated with vendeur role

== Product Management ==
V -> PM: Access product management module
PM -> DBP: Retrieve existing products
DBP -> PM: Return product catalog
PM -> V: Display product catalog

V -> PM: Add new product (description, price)
PM -> DBP: Save new product
DBP -> PM: Product saved successfully
PM -> V: Product creation confirmed

V -> PM: Modify existing product
PM -> DBP: Update product information
DBP -> PM: Product updated successfully
PM -> V: Product modification confirmed

V -> SM: Manage stock (add/subtract/consult)
SM -> DBP: Update stock levels
DBP -> SM: Stock updated
SM -> V: Stock management completed

== Client Management ==
V -> CM: Access client management module
CM -> DBC: Retrieve existing clients
DBC -> CM: Return client database
CM -> V: Display client list

V -> CM: Create new client with information
CM -> DBC: Save new client
DBC -> CM: Client saved successfully
CM -> V: Client creation confirmed

V -> CM: Search existing clients
CM -> DBC: Query client database
DBC -> CM: Return search results
CM -> V: Display search results

V -> CM: Modify client information
CM -> DBC: Update client data
DBC -> CM: Client updated successfully
CM -> V: Client modification confirmed

V -> CM: Delete client if necessary
CM -> DBC: Remove client record
DBC -> CM: Client deleted successfully
CM -> V: Client deletion confirmed

@enduml

@startuml Table2_Subscription_Management
title Table 2 - Subscription Creation and Surveillance (Stories 3.3, 3.4)

actor Administrateur as A
actor "Responsable d'entreprise" as R
participant "Admin System" as AS
participant "Subscription Creation Module" as SCM
participant "Subscription Surveillance Module" as SSM
participant "Alert System" as AlertS
participant "MongoDB Subscriptions" as DBS
participant "Account Management" as AM

== Admin Subscription Creation ==
A -> AS: Login to admin system
AS -> A: Admin authentication successful

A -> SCM: Access subscription creation module
SCM -> A: Display subscription creation interface

A -> SCM: Define variable subscription durations
SCM -> DBS: Save duration configurations
DBS -> SCM: Durations saved successfully

A -> SCM: Create personalized subscriptions
SCM -> DBS: Save subscription details
DBS -> SCM: Subscriptions created successfully

A -> SCM: Assign subscriptions to enterprise managers
SCM -> DBS: Link subscriptions to managers
DBS -> SCM: Assignments completed
SCM -> A: Subscription assignment confirmed

A -> SSM: Configure automatic subscription surveillance
SSM -> AlertS: Setup automatic alerts
AlertS -> SSM: Alert system configured
SSM -> A: Surveillance system activated

== Automatic Account Blocking ==
SSM -> DBS: Check subscription expiration dates
DBS -> SSM: Return expired subscriptions
SSM -> AM: Block expired accounts automatically
AM -> DBS: Update account status to blocked
DBS -> AM: Account status updated

== Enterprise Manager Consultation ==
R -> AS: Login to enterprise account
AS -> R: Enterprise authentication successful

R -> SSM: Consult subscription status
SSM -> DBS: Retrieve subscription information
DBS -> SSM: Return subscription details
SSM -> R: Display subscription status and limits

R -> SSM: Monitor usage limits
SSM -> DBS: Check current usage
DBS -> SSM: Return usage statistics
SSM -> R: Display usage monitoring

== Automatic Alerts and Renewal ==
AlertS -> R: Send automatic alert before expiration
R -> AlertS: Acknowledge alert received

R -> SCM: Request subscription renewal
SCM -> A: Forward renewal request
A -> SCM: Process renewal request
SCM -> DBS: Update subscription
DBS -> SCM: Renewal completed
SCM -> R: Renewal confirmation sent

@enduml

@startuml Table3_Payment_Delivery_Management
title Table 3 - Payment and Delivery Management (Stories 3.5, 3.6)

actor Utilisateur as U
actor Vendeur as V
actor "Responsable d'entreprise" as R
actor Livreur as L
participant "Payment Management Module" as PMM
participant "Delivery Management Module" as DMM
participant "Invoice System" as IS
participant "Delivery Note System" as DNS
participant "Statistics Module" as SM
participant "MongoDB Payments" as DBP
participant "MongoDB Deliveries" as DBD
participant "MongoDB Invoices" as DBI

== Payment Management ==
U -> PMM: Access payment management module
PMM -> DBI: Retrieve pending invoices
DBI -> PMM: Return invoice list
PMM -> U: Display invoices to process

U -> PMM: Select invoice to process
PMM -> DBI: Retrieve invoice details
DBI -> PMM: Return invoice information
PMM -> U: Display invoice details

U -> PMM: Choose payment method (transfer/check/cash)
PMM -> U: Display payment method options

U -> PMM: Record received payment
PMM -> DBP: Save payment information
DBP -> PMM: Payment recorded successfully
PMM -> DBI: Update invoice status to paid
DBI -> PMM: Invoice status updated
PMM -> U: Payment recording confirmed

U -> PMM: Track invoice status (paid/unpaid)
PMM -> DBI: Query invoice statuses
DBI -> PMM: Return status information
PMM -> U: Display invoice tracking

== Delivery Management ==
V -> DMM: Access delivery management module
DMM -> DBD: Retrieve available deliverers
DBD -> DMM: Return deliverer list
DMM -> V: Display available deliverers

V -> DNS: Create delivery note for invoice
DNS -> DBI: Link delivery note to invoice
DBI -> DNS: Delivery note created
DNS -> V: Delivery note creation confirmed

V -> DMM: Assign deliverer to delivery
DMM -> DBD: Assign deliverer to delivery
DBD -> DMM: Assignment completed
DMM -> L: Notify deliverer of assignment
DMM -> V: Deliverer assignment confirmed

V -> DMM: Track delivery status in real-time
DMM -> DBD: Query delivery status
DBD -> DMM: Return current status
DMM -> V: Display delivery tracking

== Statistics and Performance ==
R -> SM: Consult deliverer performance statistics
SM -> DBD: Retrieve delivery data
DBD -> SM: Return performance metrics
SM -> R: Display performance statistics

R -> SM: Generate detailed delivery reports
SM -> DBD: Query comprehensive delivery data
DBD -> SM: Return detailed information
SM -> R: Generate and display reports

@enduml

@startuml Table4_System_Template_Configuration
title Table 4 - System Configuration and Template Management (Stories 3.7, 3.8)

actor Administrateur as A
actor "Responsable d'entreprise" as R
participant "Admin Panel" as AP
participant "System Configuration Module" as SCM
participant "Security Settings" as SS
participant "Template Management Module" as TMM
participant "Template Customization Module" as TCM
participant "MongoDB Config" as DBC
participant "MongoDB Templates" as DBT
participant "File Storage" as FS

== System Configuration by Admin ==
A -> AP: Login to admin panel
AP -> A: Admin authentication successful

A -> SCM: Access system configuration settings
SCM -> DBC: Retrieve current configuration
DBC -> SCM: Return system parameters
SCM -> A: Display configuration interface

A -> SCM: Configure general system options
SCM -> DBC: Update general settings
DBC -> SCM: Settings updated successfully

A -> SS: Define security parameters
SS -> DBC: Save security configuration
DBC -> SS: Security settings saved
SS -> A: Security configuration confirmed

A -> TMM: Create base templates (standard and modern)
TMM -> DBT: Save base template structures
DBT -> TMM: Base templates created
TMM -> A: Base template creation confirmed

A -> SCM: Save system configuration
SCM -> DBC: Persist all configurations
DBC -> SCM: Configuration saved successfully
SCM -> A: System configuration completed

== Template Customization by Enterprise Manager ==
R -> AP: Login to enterprise account
AP -> R: Enterprise authentication successful

R -> TCM: Access template management module
TCM -> DBT: Retrieve available base templates
DBT -> TCM: Return template options
TCM -> R: Display base templates (standard/modern)

R -> TCM: Select base template (standard or modern)
TCM -> DBT: Load selected template structure
DBT -> TCM: Return template details
TCM -> R: Display template customization interface

R -> TCM: Customize colors according to brand guidelines
TCM -> DBT: Update template color scheme
DBT -> TCM: Colors updated successfully

R -> TCM: Add company logo
TCM -> FS: Upload and store logo file
FS -> TCM: Logo stored successfully
TCM -> DBT: Link logo to template
DBT -> TCM: Logo association completed

R -> TCM: Apply personalized template to documents
TCM -> DBT: Save customized template
DBT -> TCM: Template customization saved
TCM -> R: Template application confirmed

R -> TCM: Preview and validate modifications
TCM -> DBT: Generate template preview
DBT -> TCM: Return preview data
TCM -> R: Display template preview
R -> TCM: Validate and confirm changes
TCM -> DBT: Finalize template customization
DBT -> TCM: Template finalized successfully
TCM -> R: Template customization completed

@enduml
